defmodule Repobot.Workers.RepositoryFilesWorkerContentOptimizationTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  import Mox
  import Repobot.Test.Fixtures

  alias Repobot.Workers.RepositoryFilesWorker

  setup :verify_on_exit!

  describe "content refresh optimization" do
    setup do
      user = create_user()

      # Create repositories with files
      repo1 =
        create_repository(%{
          name: "repo1",
          owner: user.login,
          full_name: "#{user.login}/repo1",
          organization_id: user.default_organization_id
        })

      repo2 =
        create_repository(%{
          name: "repo2",
          owner: user.login,
          full_name: "#{user.login}/repo2",
          organization_id: user.default_organization_id
        })

      # Create files - some common, some unique
      # Common file: README.md
      create_repository_file(%{
        path: "README.md",
        # No content yet
        content: nil,
        size: 100,
        type: "file",
        sha: "readme1",
        repository_id: repo1.id
      })

      create_repository_file(%{
        path: "README.md",
        # No content yet
        content: nil,
        size: 100,
        type: "file",
        sha: "readme2",
        repository_id: repo2.id
      })

      # Unique files
      create_repository_file(%{
        path: "unique1.txt",
        content: nil,
        size: 50,
        type: "file",
        sha: "unique1",
        repository_id: repo1.id
      })

      create_repository_file(%{
        path: "unique2.txt",
        content: nil,
        size: 75,
        type: "file",
        sha: "unique2",
        repository_id: repo2.id
      })

      %{user: user, repo1: repo1, repo2: repo2}
    end

    test "refreshes content for all files when no file_paths specified", %{
      user: user,
      repo1: repo1,
      repo2: repo2
    } do
      # Check how many files we actually have
      repo1_with_files = Repobot.Repo.preload(repo1, :files)
      repo2_with_files = Repobot.Repo.preload(repo2, :files)
      total_files = length(repo1_with_files.files) + length(repo2_with_files.files)

      # Mock GitHub API to return content for all files
      Repobot.Test.GitHubMock
      |> stub(:client, fn _user -> :test_client end)
      |> expect(:get_file_content, total_files, fn :test_client, _owner, _repo, path ->
        case path do
          "README.md" -> {:ok, "# README content", %{}}
          "unique1.txt" -> {:ok, "unique1 content", %{}}
          "unique2.txt" -> {:ok, "unique2 content", %{}}
          _ -> {:ok, "default content", %{}}
        end
      end)

      # Enqueue content refresh without file_paths (should refresh all files)
      assert {:ok, job} =
               RepositoryFilesWorker.enqueue_content_refresh(
                 user.id,
                 [repo1.id, repo2.id],
                 "test_topic"
               )

      # Perform the job
      assert :ok = perform_job(RepositoryFilesWorker, job.args)
    end

    test "refreshes content only for specified file_paths when provided", %{
      user: user,
      repo1: repo1,
      repo2: repo2
    } do
      # Count how many README.md files we have (should be 2, one in each repo)
      repo1_with_files = Repobot.Repo.preload(repo1, :files)
      repo2_with_files = Repobot.Repo.preload(repo2, :files)

      readme_files_count =
        Enum.count(repo1_with_files.files, &(&1.path == "README.md")) +
          Enum.count(repo2_with_files.files, &(&1.path == "README.md"))

      # Mock GitHub API to return content only for README.md (common file)
      Repobot.Test.GitHubMock
      |> stub(:client, fn _user -> :test_client end)
      |> expect(:get_file_content, readme_files_count, fn :test_client, _owner, _repo, path ->
        case path do
          "README.md" -> {:ok, "# README content", %{}}
          _ -> {:error, :file_not_found}
        end
      end)

      # Enqueue content refresh with specific file_paths (only common files)
      assert {:ok, job} =
               RepositoryFilesWorker.enqueue_content_refresh(
                 user.id,
                 [repo1.id, repo2.id],
                 "test_topic",
                 # Only refresh this common file
                 ["README.md"]
               )

      # Perform the job
      assert :ok = perform_job(RepositoryFilesWorker, job.args)
    end
  end
end
