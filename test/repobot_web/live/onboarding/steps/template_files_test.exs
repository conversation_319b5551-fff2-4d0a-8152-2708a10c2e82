defmodule RepobotWeb.Live.Onboarding.Steps.TemplateFilesTest do
  use RepobotWeb.ConnCase, async: true
  use Repobot.Test.Fixtures

  import Phoenix.LiveViewTest
  import Mox

  alias Repobot.Test.GitHubMock
  alias Repobot.Accounts.User.Settings
  alias RepobotWeb.Live.Onboarding.Steps.TemplateFiles

  setup :verify_on_exit!

  describe "template files step" do
    setup do
      user = create_user()
      user = %{user | settings: %Settings{onboarding_completed: false}}

      # Create template repository (empty - will need refresh)
      template_repo =
        create_repository(%{
          name: "template-repo",
          owner: user.login,
          full_name: "#{user.login}/template-repo",
          organization_id: user.default_organization_id,
          data: %{"default_branch" => "main"}
        })

      # Create target repositories with common files (already have files - should not need refresh)
      sync_repo_1 =
        create_repository(%{
          name: "sync-repo-1",
          owner: user.login,
          full_name: "#{user.login}/sync-repo-1",
          organization_id: user.default_organization_id
        })

      sync_repo_2 =
        create_repository(%{
          name: "sync-repo-2",
          owner: user.login,
          full_name: "#{user.login}/sync-repo-2",
          organization_id: user.default_organization_id
        })

      # Create common files in both target repositories
      common_content = "This is common content."

      create_repository_file(%{
        path: "common.txt",
        content: common_content,
        size: String.length(common_content),
        type: "file",
        sha: "common123",
        repository_id: sync_repo_1.id
      })

      create_repository_file(%{
        path: "common.txt",
        content: common_content,
        size: String.length(common_content),
        type: "file",
        sha: "common123",
        repository_id: sync_repo_2.id
      })

      # Preload files
      template_repo = Repobot.Repo.preload(template_repo, :files)
      sync_repo_1 = Repobot.Repo.preload(sync_repo_1, :files)
      sync_repo_2 = Repobot.Repo.preload(sync_repo_2, :files)

      {
        :ok,
        user: user,
        template_repo: template_repo,
        sync_repo_1: sync_repo_1,
        sync_repo_2: sync_repo_2,
        _common_content: common_content
      }
    end

    test "repositories are properly reloaded with fresh content after content refresh",
         %{
           sync_repo_1: sync_repo_1,
           sync_repo_2: sync_repo_2
         } do
      # This test verifies that list_repositories_by_ids properly loads fresh file content
      # after content refresh, fixing the race condition where stale repository data
      # was used for similarity calculation

      # Create identical files in both repositories
      identical_content = "defmodule Test do\n  def hello, do: :world\nend\n"

      # Create LICENSE files with content
      create_repository_file(%{
        path: "LICENSE",
        content: identical_content,
        size: String.length(identical_content),
        type: "file",
        sha: "license123",
        repository_id: sync_repo_1.id
      })

      create_repository_file(%{
        path: "LICENSE",
        content: identical_content,
        size: String.length(identical_content),
        type: "file",
        sha: "license123",
        repository_id: sync_repo_2.id
      })

      # Test that list_repositories_by_ids now properly loads files
      repository_ids = [sync_repo_1.id, sync_repo_2.id]
      refreshed_repos = Repobot.Repositories.list_repositories_by_ids(repository_ids)

      # Verify that files are properly loaded
      assert length(refreshed_repos) == 2

      repo_1 = Enum.find(refreshed_repos, &(&1.id == sync_repo_1.id))
      repo_2 = Enum.find(refreshed_repos, &(&1.id == sync_repo_2.id))

      # Files should be loaded (not empty associations)
      assert repo_1.files != []
      assert repo_2.files != []

      # Should find LICENSE files with content
      license_file_1 = Enum.find(repo_1.files, &(&1.path == "LICENSE"))
      license_file_2 = Enum.find(repo_2.files, &(&1.path == "LICENSE"))

      assert license_file_1 != nil
      assert license_file_2 != nil
      assert license_file_1.content == identical_content
      assert license_file_2.content == identical_content

      # Now test similarity calculation with the properly loaded repositories
      {:ok, common_files} = Repobot.Files.find_common_files(refreshed_repos)

      # Should find both common.txt and LICENSE
      assert length(common_files) == 2

      test_pid = self()

      :ok =
        Repobot.Files.calculate_common_files_similarity(
          common_files,
          refreshed_repos,
          test_pid
        )

      # Wait for similarity calculation to complete
      receive do
        {:similarity_complete, result} ->
          # Both files should be present with correct similarity
          assert length(result) == 2

          license_result = Enum.find(result, &(&1["path"] == "LICENSE"))
          common_result = Enum.find(result, &(&1["path"] == "common.txt"))

          assert license_result != nil
          assert license_result["similarity"] == 100
          assert license_result["content"] == identical_content

          assert common_result != nil
          assert common_result["similarity"] == 100

        {:similarity_error, reason} ->
          flunk("Similarity calculation failed: #{reason}")
      after
        5000 ->
          flunk("Similarity calculation timed out")
      end
    end

    test "selecting a file does not trigger unnecessary refresh", %{
      conn: conn,
      user: user,
      template_repo: template_repo,
      sync_repo_1: sync_repo_1,
      sync_repo_2: sync_repo_2,
      _common_content: _common_content
    } do
      # Mock AI for tag inference in Summary step
      Repobot.Test.AIMock
      |> stub(:infer_tags, fn _source_file, _organization -> {:ok, []} end)

      # Mock GitHub API - we should only see these calls during initial load
      # and NOT when files are selected/deselected
      GitHubMock
      |> stub(:client, fn _user_param -> :test_client end)
      # Only template_repo (empty) will be refreshed, sync repos already have files
      |> expect(:get_tree, 1, fn :test_client, _owner, repo_name ->
        case repo_name do
          # Empty repository
          "template-repo" -> {:ok, []}
          _ -> raise "Unexpected repo refresh: #{repo_name}"
        end
      end)
      # Only sync_repo_1 and sync_repo_2 have files to refresh content for
      |> expect(:get_file_content, 2, fn :test_client, _owner, repo_name, path ->
        repo =
          cond do
            repo_name == sync_repo_1.name -> sync_repo_1
            repo_name == sync_repo_2.name -> sync_repo_2
            true -> raise "Unexpected repo: #{repo_name}"
          end

        file = Enum.find(repo.files, &(&1.path == path))

        if file do
          {:ok, file.content, %{}}
        else
          {:error, :file_not_found}
        end
      end)

      # Start onboarding and navigate to template files step
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Navigate through steps to reach template files
      # Welcome -> Template Repository
      view |> element("button", "Next") |> render_click()
      view |> element("#select_existing") |> render_click()
      view |> element("input[value='#{template_repo.id}']") |> render_click()
      # Template Repository -> Repository Sync
      view |> element("button", "Next") |> render_click()
      view |> element("button[phx-value-id='#{sync_repo_1.id}']") |> render_click()
      view |> element("button[phx-value-id='#{sync_repo_2.id}']") |> render_click()
      # Repository Sync -> Template Files
      view |> element("button", "Next") |> render_click()

      # Wait for common files to be loaded
      assert_eventually(fn ->
        refute has_element?(view, "p", "Loading common files...")
      end)

      # Verify the common file is found and listed
      assert_eventually(fn ->
        assert has_element?(view, "ul[data-testid='common-files-list']")
        assert has_element?(view, "span[data-testid='common-file-name']", "common.txt")
      end)

      # Now select the file - this should NOT trigger any additional GitHub API calls
      # The mock will fail if more than the expected 3 calls are made
      view |> element("input[phx-value-path='common.txt']") |> render_click()

      # Verify the file is selected
      assert has_element?(view, "input[phx-value-path='common.txt'][checked]")

      # Deselect the file - this should also NOT trigger additional API calls
      view |> element("input[phx-value-path='common.txt']") |> render_click()

      # Verify the file is deselected
      refute has_element?(view, "input[phx-value-path='common.txt'][checked]")

      # Select it again
      view |> element("input[phx-value-path='common.txt']") |> render_click()

      # Verify the file is selected again
      assert has_element?(view, "input[phx-value-path='common.txt'][checked]")

      # Proceed to next step - this should finalize the step
      view |> element("button", "Next") |> render_click()

      # Verify we moved to the summary step
      assert_eventually(fn ->
        assert has_element?(view, "h2", "Setup Summary")
      end)
    end

    test "reproduces issue: similarity calculation fails when repositories are fetched for first time",
         %{
           user: user,
           template_repo: template_repo
         } do
      # This test reproduces the issue where:
      # 1. Repositories have no files initially (need tree loading from GitHub)
      # 2. TemplateFiles step starts tree loading via worker
      # 3. After tree loading, it finds common files and starts content refresh
      # 4. After content refresh, similarity calculation fails because it uses stale data

      # Create target repositories WITHOUT files (simulating first-time fetch)
      target_repo_1 =
        create_repository(%{
          name: "target-1",
          owner: user.login,
          full_name: "#{user.login}/target-1",
          organization_id: user.default_organization_id,
          data: %{"default_branch" => "main"}
        })

      target_repo_2 =
        create_repository(%{
          name: "target-2",
          owner: user.login,
          full_name: "#{user.login}/target-2",
          organization_id: user.default_organization_id,
          data: %{"default_branch" => "main"}
        })

      # Verify repositories start with no files
      assert Enum.empty?(Repobot.Repo.preload(target_repo_1, :files).files)
      assert Enum.empty?(Repobot.Repo.preload(target_repo_2, :files).files)
      assert Enum.empty?(Repobot.Repo.preload(template_repo, :files).files)

      # Mock GitHub API to return common files for target repos
      common_content = "# Common README\nThis is shared content."

      GitHubMock
      |> stub(:client, fn _user -> :test_client end)
      # Tree loading calls
      |> expect(:get_tree, 3, fn :test_client, _owner, repo_name ->
        case repo_name do
          "target-1" ->
            {:ok,
             [
               %{"path" => "README.md", "type" => "file", "sha" => "readme1", "size" => 100},
               %{"path" => "LICENSE", "type" => "file", "sha" => "license1", "size" => 200}
             ]}

          "target-2" ->
            {:ok,
             [
               %{"path" => "README.md", "type" => "file", "sha" => "readme2", "size" => 100},
               %{"path" => "config.json", "type" => "file", "sha" => "config2", "size" => 50}
             ]}

          "template-repo" ->
            {:ok, []}
        end
      end)
      # Content refresh calls - README.md is common to both target repos
      # After the fix, the content refresh will be triggered for all files in all repos
      # that need content (files without content_updated_at)
      |> expect(:get_file_content, 4, fn :test_client, _owner, repo_name, path ->
        case {repo_name, path} do
          {"target-1", "README.md"} -> {:ok, common_content, %{}}
          {"target-2", "README.md"} -> {:ok, common_content, %{}}
          {"target-1", "LICENSE"} -> {:ok, "License content", %{}}
          {"target-2", "config.json"} -> {:ok, "{}", %{}}
          _ -> {:error, :file_not_found}
        end
      end)

      # Create onboarding state with empty repositories
      state = %{
        template_repo: template_repo,
        selected_repos: [target_repo_1, target_repo_2],
        current_step: :template_files
      }

      socket = %Phoenix.LiveView.Socket{
        transport_pid: self(),
        assigns: %{
          __changed__: %{},
          current_user: user,
          current_organization: user.default_organization,
          state: state,
          common_files: [],
          loading_common_files: false,
          common_files_error: nil,
          loading_progress: 0,
          loading_message: "",
          content_refresh_progress: 0,
          similarity_progress: 0,
          refreshed_repos: nil,
          excluded_existing_files: []
        }
      }

      # Subscribe to Oban notifications for repository files
      topic = "repository_files:#{user.id}"
      topic_atom = String.to_atom(topic)
      Oban.Notifier.listen(Oban, topic_atom)

      # Start the TemplateFiles step - this should trigger tree loading
      {:ok, updated_socket} = TemplateFiles.update(%{}, socket)

      # Should be loading
      assert updated_socket.assigns.loading_common_files == true

      # Wait for tree loading completion notification
      assert_receive {:notification, _channel,
                      %{
                        "event" => "repository_files_complete",
                        "result" => %{
                          "status" => "ok",
                          "operation" => "load_trees"
                        }
                      }},
                     5000

      # Simulate the tree loading completion message to the TemplateFiles component
      tree_complete_message = %{
        repository_files_complete: %{
          "status" => "ok",
          "operation" => "load_trees",
          "repository_ids" => [target_repo_1.id, target_repo_2.id, template_repo.id]
        }
      }

      {:ok, socket_after_trees} = TemplateFiles.update(tree_complete_message, updated_socket)

      # After the fix: Should now find common files because repositories are reloaded from database
      assert socket_after_trees.assigns.common_files != []

      # Wait for content refresh completion
      assert_receive {:notification, _channel,
                      %{
                        "event" => "repository_files_complete",
                        "result" => %{
                          "status" => "ok",
                          "operation" => "refresh_content",
                          "repository_ids" => repository_ids
                        }
                      }},
                     5000

      # Simulate content refresh completion message - use the same format as received
      content_complete_message = %{
        repository_files_complete: %{
          "status" => "ok",
          "operation" => "refresh_content",
          "repository_ids" => repository_ids
        }
      }

      {:ok, _socket_after_content} =
        TemplateFiles.update(content_complete_message, socket_after_trees)

      # Now wait for similarity calculation to complete
      # After the fix, this should work correctly
      # The similarity messages come as {:files, :template_files, message}
      receive do
        {:files, :template_files, {:similarity_complete, files}} ->
          # The similarity calculation should now work
          # The files should have content and similarity scores
          assert length(files) > 0

          readme_file = Enum.find(files, &(&1["path"] == "README.md"))
          assert readme_file != nil
          assert readme_file["content"] == common_content
          assert readme_file["similarity"] == 100

        {:files, :template_files, {:similarity_error, reason}} ->
          flunk("Similarity calculation failed: #{reason}")
      after
        10000 ->
          flunk("Similarity calculation timed out")
      end
    end

    test "filters out files with less than 100% similarity", %{
      sync_repo_1: sync_repo_1,
      sync_repo_2: sync_repo_2
    } do
      # Create files with different content (not identical)
      content_1 = "defmodule Test do\n  def hello, do: :world\nend\n"
      # Different content
      content_2 = "defmodule Test do\n  def hello, do: :universe\nend\n"

      # Create similar but not identical files
      create_repository_file(%{
        path: "similar.ex",
        content: content_1,
        size: String.length(content_1),
        type: "file",
        sha: "similar1",
        repository_id: sync_repo_1.id
      })

      create_repository_file(%{
        path: "similar.ex",
        content: content_2,
        size: String.length(content_2),
        type: "file",
        sha: "similar2",
        repository_id: sync_repo_2.id
      })

      # Also create an identical file for comparison
      identical_content = "# Identical file\nThis is the same."

      create_repository_file(%{
        path: "identical.md",
        content: identical_content,
        size: String.length(identical_content),
        type: "file",
        sha: "identical123",
        repository_id: sync_repo_1.id
      })

      create_repository_file(%{
        path: "identical.md",
        content: identical_content,
        size: String.length(identical_content),
        type: "file",
        sha: "identical123",
        repository_id: sync_repo_2.id
      })

      # Get fresh repositories with files
      repository_ids = [sync_repo_1.id, sync_repo_2.id]
      refreshed_repos = Repobot.Repositories.list_repositories_by_ids(repository_ids)

      # Find common files (should include both similar.ex and identical.md)
      {:ok, common_files} = Repobot.Files.find_common_files(refreshed_repos)

      # Should find all common files by path
      assert length(common_files) >= 2
      assert Enum.any?(common_files, &(&1["path"] == "similar.ex"))
      assert Enum.any?(common_files, &(&1["path"] == "identical.md"))

      test_pid = self()

      # Calculate similarity
      :ok =
        Repobot.Files.calculate_common_files_similarity(
          common_files,
          refreshed_repos,
          test_pid
        )

      # Wait for similarity calculation to complete
      receive do
        {:similarity_complete, result} ->
          # Verify that similar.ex has less than 100% similarity
          similar_result = Enum.find(result, &(&1["path"] == "similar.ex"))
          identical_result = Enum.find(result, &(&1["path"] == "identical.md"))

          assert similar_result != nil
          assert identical_result != nil

          # The similar file should have less than 100% similarity
          assert similar_result["similarity"] < 100
          # The identical file should have 100% similarity
          assert identical_result["similarity"] == 100

        {:similarity_error, reason} ->
          flunk("Similarity calculation failed: #{reason}")
      after
        5000 ->
          flunk("Similarity calculation timed out")
      end

      # Now test the filtering in the TemplateFiles component
      # Create a mock socket with the similarity results
      socket = %Phoenix.LiveView.Socket{
        assigns: %{
          __changed__: %{},
          common_files: [],
          loading_common_files: true
        }
      }

      # Simulate the similarity_complete message with mixed similarity results
      files_with_mixed_similarity = [
        %{"path" => "similar.ex", "similarity" => 85, "content" => content_1},
        %{"path" => "identical.md", "similarity" => 100, "content" => identical_content}
      ]

      {:ok, updated_socket} =
        TemplateFiles.update(
          %{files_message: {:similarity_complete, files_with_mixed_similarity}},
          socket
        )

      # Should only include the 100% similar file
      assert length(updated_socket.assigns.common_files) == 1
      assert hd(updated_socket.assigns.common_files)["path"] == "identical.md"
      assert hd(updated_socket.assigns.common_files)["similarity"] == 100
    end
  end
end
